{ config, pkgs, ... }:

let
  unstable = import <nixos-unstable> {
    config = config.nixpkgs.config;
  };
in

{
  imports = [
    ./hardware-configuration.nix
    <home-manager/nixos>
  ];

  # Hardware Configuration
  hardware.sensor.iio.enable = true;

  # System Configuration
  system.stateVersion = "25.05";
  system.autoUpgrade = {
    enable = true;
    allowReboot = false;
    dates = "00:00:00";
    persistent = true;
  };

  nix.gc = {
    automatic = true;
    dates = "daily";
    options = "--delete-older-than 7d";
  };

  boot = {
    loader = {
      systemd-boot.enable = true;
      efi.canTouchEfiVariables = true;
    };
    kernelPackages = pkgs.linuxPackages_zen;
  };

  # Network Configuration
  networking = {
    hostName = "iDell";
    networkmanager.enable = true;
    # AdGuard DNS servers for ad-blocking and privacy
    nameservers = [ "************" "************" ];
    firewall = {
      allowedTCPPorts = [ 22000 8384 1714 1764 8080 39630 54104 11434 9000 8000 ];
      allowedUDPPorts = [ 22000 21027 1714 1764 1716 ];
    };
  };

  # Localization
  time.timeZone = "Africa/Algiers";
  i18n = {
    defaultLocale = "en_US.UTF-8";
    extraLocaleSettings = {
      LC_ADDRESS = "ar_DZ.UTF-8";
      LC_IDENTIFICATION = "ar_DZ.UTF-8";
      LC_MEASUREMENT = "ar_DZ.UTF-8";
      LC_MONETARY = "ar_DZ.UTF-8";
      LC_NAME = "ar_DZ.UTF-8";
      LC_NUMERIC = "ar_DZ.UTF-8";
      LC_PAPER = "ar_DZ.UTF-8";
      LC_TELEPHONE = "ar_DZ.UTF-8";
      LC_TIME = "ar_DZ.UTF-8";
    };
  };

  # Desktop Environment
  services.xserver = {
    enable = true;
    xkb = {
      layout = "fr";
      variant = "";
    };
  };
  services.displayManager.sddm.enable = true;
  services.desktopManager.plasma6.enable = true;

  # Enable KDE Applications
  programs.kdeconnect.enable = true;
  environment.plasma6.excludePackages = [];
  programs.partition-manager.enable = true;

  # System Services
  services = {
    printing.enable = false;
    libinput.enable = true;
    openssh.enable = true;
    fprintd.enable = true;

    pipewire = {
      enable = true;
      alsa = {
        enable = true;
        support32Bit = true;
      };
      pulse.enable = true;
    };

    power-profiles-daemon.enable = true;

    # Syncthing file synchronization service
    syncthing = {
      enable = true;
      user = "yusuf";
      dataDir = "/home/<USER>/.local/share/syncthing";
      configDir = "/home/<USER>/.config/syncthing";
      openDefaultPorts = true;
      settings = {
        # Devices configuration
        devices = {
          "iPoco" = {
            id = "VLCZOWO-JNGTHN6-QC5AGAZ-ZSRIBRN-5HJOPX6-OC4AE4Z-PMDRJW3-FUWP6Q3";
            addresses = [ "dynamic" ];
          };
          "iDell" = {
            id = "VPXAMC7-NSFBWYL-MJRN454-7X3F4GT-ZP5BRVH-BYPK75J-USIYEEI-56XJTAC";
            addresses = [ "dynamic" ];
          };
        };

        # Folders configuration
        folders = {
          "Videos" = {
            id = "namry-5ckpj";
            path = "/home/<USER>/iData/Videos";
            devices = [ "iPoco" "iDell" ];
            type = "sendreceive";
            rescanIntervalS = 3600;
            fsWatcherEnabled = true;
            ignorePerms = false;
          };
          "iDoc" = {
            id = "utet2-mdwaw";
            path = "/home/<USER>/iData/iDoc";
            devices = [ "iPoco" "iDell" ];
            type = "sendreceive";
            rescanIntervalS = 3600;
            fsWatcherEnabled = true;
            ignorePerms = false;
          };
        };

        # GUI configuration
        gui = {
          user = "yusuf";
          password = ""; # Empty password for local access only
          address = "127.0.0.1:8384";
          tls = false;
        };

        # Options configuration
        options = {
          urAccepted = -1; # Disable usage reporting
          globalAnnounceEnabled = true;
          localAnnounceEnabled = true;
          localAnnouncePort = 21027;
          relaysEnabled = true;
          startBrowser = true;
          natEnabled = true;
          crashReportingEnabled = true;
          autoUpgradeIntervalH = 12;
          keepTemporariesH = 24;
          limitBandwidthInLan = false;
          setLowPriority = true;
        };
      };
    };

  };

  # User Configuration
  users.users.yusuf = {
    isNormalUser = true;
    description = "yusuf";
    extraGroups = [ "networkmanager" "wheel" "fprintd" "adbusers" "docker" ];
    shell = pkgs.zsh;
  };

  # System Programs and Features
  environment.systemPackages = with pkgs; [
    fprintd
  ];

  programs = {
    zsh.enable = true;
    adb.enable = true;
    nix-ld.enable = true;
  };

  # Environment variables for Flutter development
  environment.variables = {
    ANDROID_HOME = "$HOME/Android/Sdk";
    ANDROID_SDK_ROOT = "$HOME/Android/Sdk";
    FLUTTER_ROOT = "${unstable.flutter}";
  };

  security.rtkit.enable = true;

  nixpkgs.config = {
    allowUnfree = true;
    android_sdk.accept_license = true;
  };

  fonts = {
    enableDefaultPackages = true;
    packages = with pkgs; [
      noto-fonts
      noto-fonts-cjk-sans
      noto-fonts-emoji
      noto-fonts-extra
      corefonts
      powerline-fonts
    ];
    fontconfig = {
      defaultFonts = {
        serif = [ "Noto Serif" ];
        sansSerif = [ "Noto Sans" ];
        monospace = [ "Noto Sans Mono" ];
        emoji = [ "Noto Color Emoji" ];
      };
    };
  };

  # Home Manager Configuration
  home-manager = {
    backupFileExtension = "backup";
    users.yusuf = { pkgs, ... }: {
      nixpkgs.config.allowUnfree = true;
      home.stateVersion = "25.05";
      home.packages = with pkgs; [
        unstable.vscode
        nixd
        nixpkgs-fmt
        kdePackages.applet-window-buttons6
        kdePackages.kdeconnect-kde
        syncthingtray
        backintime
        sshfs
        vlc
        libreoffice
        obsidian
        scrcpy
        usbimager
        qbittorrent-enhanced
        unstable.code-cursor
        unstable.windsurf
        unstable.zed-editor
        unstable.gemini-cli
        jdk17
        unstable.flutter
        unstable.android-studio
        unstable.android-tools
        (writeScriptBin "retry-until-success" ''
          #!${pkgs.zsh}/bin/zsh
          if [ $# -eq 0 ]; then
              echo "Usage: $0 'command to execute'"
              exit 1
          fi
          command_to_run="$@"
          attempt=1
          echo "Starting to execute: $command_to_run"
          while true; do
              # Execute the command
              if eval "$command_to_run"; then
                  echo "Command succeeded on attempt $attempt"
                  exit 0
              else
                  echo "Attempt $attempt failed. Retrying..."
                  attempt=$((attempt + 1))
                  sleep 1
              fi
          done
        '')
      ];

      programs = {
      browserpass.enable = true;
      brave = {
        enable = true;
        extensions = [
          { id = "cjpalhdlnbpafiamejdnhcphjbkeiagm"; } # uBlock Origin
          { id = "eimadpbcbfnmbkopoojfekhnkhdbieeh"; } # Dark Reader
          { id = "nngceckbapebfimnlniiiahkandclblb"; } # Bitwarden
          { id = "kchgllkpfcggmdaoopkhlkbcokngahlg"; } # DF Tube
          { id = "pehaalcefcjfccdpbckoablngfkfgfgj"; } # Block image
          { id = "mnjggcdmjocbbbhaepdhchncahnbgone"; } # SponsorBlock for YouTube
          { id = "ijlnjklmlhhfodgfpidpnccipnodohgl"; } # YouTube Silence Skipper
        ];

        # Browser preferences based on your current settings
        commandLineArgs = [
          # Privacy and security settings
          "--disable-background-networking"
          "--disable-background-timer-throttling"
          "--disable-backgrounding-occluded-windows"
          "--disable-breakpad"
          "--disable-client-side-phishing-detection"
          "--disable-component-extensions-with-background-pages"
          "--disable-default-apps"
          "--disable-dev-shm-usage"
          "--disable-extensions-http-throttling"
          "--disable-features=TranslateUI,BlinkGenPropertyTrees"
          "--disable-hang-monitor"
          "--disable-ipc-flooding-protection"
          "--disable-popup-blocking"
          "--disable-prompt-on-repost"
          "--disable-renderer-backgrounding"
          "--disable-sync"
          "--disable-web-security"
          "--metrics-recording-only"
          "--no-default-browser-check"
          "--no-first-run"
          "--no-pings"
          "--password-store=basic"
          "--use-mock-keychain"

          # UI preferences from your settings
          "--show-component-extension-options"
          "--enable-features=VaapiVideoDecoder"

          # Search engine (DuckDuckGo as default)
          "--search-engine-choice-country=DZ"
        ];
      };

      # Additional Brave browser configuration files
      home.file = {
        # Brave browser policies (based on your current settings)
        ".config/BraveSoftware/Brave-Browser/managed_preferences.json".text = builtins.toJSON {
          # Privacy settings
          "DefaultSearchProviderEnabled" = true;
          "DefaultSearchProviderName" = "DuckDuckGo";
          "DefaultSearchProviderKeyword" = ":d";
          "DefaultSearchProviderSearchURL" = "https://duckduckgo.com/?q={searchTerms}&t=brave";
          "DefaultSearchProviderSuggestURL" = "https://ac.duckduckgo.com/ac/?q={searchTerms}&type=list";

          # Security and privacy
          "PasswordManagerEnabled" = false;
          "AutofillAddressEnabled" = false;
          "AutofillCreditCardEnabled" = false;
          "SafeBrowsingEnabled" = true;
          "SafeBrowsingExtendedReportingEnabled" = false;
          "SearchSuggestEnabled" = true;
          "AlternateErrorPagesEnabled" = false;
          "SpellcheckEnabled" = true;
          "SpellcheckLanguage" = ["en-US"];

          # Brave-specific settings
          "BraveAdsEnabled" = false;
          "BraveRewardsEnabled" = false;
          "BraveShieldsEnabled" = true;
          "BraveShieldsEnabledForUrls" = ["*"];
          "BraveAdBlockEnabled" = true;
          "BraveFingerprintingBlockEnabled" = true;

          # UI preferences
          "ShowHomeButton" = true;
          "BookmarkBarEnabled" = true;
          "BrowserThemeColor" = "system";
          "NewTabPageLocation" = "chrome://newtab/";

          # Download settings
          "DownloadDirectory" = "/home/<USER>/Downloads";
          "PromptForDownloadLocation" = false;

          # Extensions (already managed by Nix)
          "ExtensionInstallBlocklist" = [];
          "ExtensionInstallAllowlist" = [
            "cjpalhdlnbpafiamejdnhcphjbkeiagm"  # uBlock Origin
            "eimadpbcbfnmbkopoojfekhnkhdbieeh"  # Dark Reader
            "nngceckbapebfimnlniiiahkandclblb"  # Bitwarden
            "kchgllkpfcggmdaoopkhlkbcokngahlg"  # DF Tube
            "pehaalcefcjfccdpbckoablngfkfgfgj"  # Block image
            "mnjggcdmjocbbbhaepdhchncahnbgone"  # SponsorBlock
            "ijlnjklmlhhfodgfpidpnccipnodohgl"  # YouTube Silence Skipper
          ];
        };

        # Additional Brave user preferences for UI and behavior
        ".config/BraveSoftware/Brave-Browser/Local State".text = builtins.toJSON {
          "brave" = {
            "stats" = {
              "ads_blocked" = "0";
              "bandwidth_saved_bytes" = "0";
            };
            "shields_settings_version" = 4;
            "new_tab_page" = {
              "shows_options" = 2;
            };
            "tabs" = {
              "vertical_tabs_enabled" = true;
              "vertical_tabs_collapsed" = true;
            };
            "sidebar" = {
              "sidebar_items" = [
                {"built_in_item_type" = 7; "type" = 0;}
                {"built_in_item_type" = 1; "type" = 0;}
                {"built_in_item_type" = 2; "type" = 0;}
                {"built_in_item_type" = 3; "type" = 0;}
                {"built_in_item_type" = 4; "type" = 0;}
              ];
            };
            "ai_chat" = {
              "show_toolbar_button" = false;
            };
            "rewards" = {
              "show_brave_rewards_button_in_location_bar" = false;
            };
            "wallet" = {
              "show_wallet_icon_on_toolbar" = false;
            };
            "today" = {
              "should_show_toolbar_button" = false;
            };
          };
          "browser" = {
            "show_home_button" = true;
            "custom_chrome_frame" = true;
          };
          "default_search_provider_data" = {
            "template_url_data" = {
              "short_name" = "DuckDuckGo";
              "keyword" = ":d";
              "url" = "https://duckduckgo.com/?q={searchTerms}&t=brave";
              "suggestions_url" = "https://ac.duckduckgo.com/ac/?q={searchTerms}&type=list";
              "favicon_url" = "https://duckduckgo.com/favicon.ico";
            };
          };
        };
      };

        git = {
          enable = true;
          extraConfig = {
            credential.helper = "store";
            user = {
              name = "Yusuf";
              email = "<EMAIL>";
            };
          };
        };

        zsh = {
          enable = true;
          autosuggestion.enable = true;
          enableCompletion = true;
          syntaxHighlighting.enable = true;

          oh-my-zsh = {
            enable = true;
            theme = "robbyrussell";
            plugins = [
              "git"
              "sudo"
              "history"
              "dirhistory"
            ];
          };

          shellAliases = {
            switch = "sudo nixos-rebuild switch";
            upgrade = "sudo nixos-rebuild switch --upgrade";
            pod = "scrcpy --new-display=1920x1080/240 --start-app=com.itunestoppodcastplayer.app";
            ls = "ls --color=auto";
            ll = "ls -la";
            retry = "retry-until-success";
          };

          initContent = ''
            # Set history size and file
            HISTSIZE=10000
            SAVEHIST=10000
            HISTFILE=~/.zsh_history

            # Basic auto/tab completion
            autoload -U compinit
            zstyle ':completion:*' menu select
            zmodload zsh/complist
            compinit
            _comp_options+=(globdots)

            # Enable searching through history
            bindkey '^r' history-incremental-pattern-search-backward
          '';
        };
      };
    };
  };
}
